"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Base Repository Module for Django Ledger
"""

from typing import (  # noqa: F401
    Any,
    Dict,
    Generic,
    List,
    Optional,
    Type,
    TypeVar,
    Union,
)
from uuid import UUID  # noqa: F401,

from django.apps import apps  # noqa: F401,
from django.core.exceptions import (  # noqa: F401,
    ObjectDoesNotExist,
    ValidationError,
)
from django.db.models import Model, QuerySet  # noqa: F401,
from django.shortcuts import get_object_or_404  # noqa: F401,

T = TypeVar("T", bound=Model)


class BaseRepository(Generic[T]):
    """
    Base Repository class providing data access patterns for all models.
    This class implements the Repository pattern for Django Ledger.

    Attributes
    ----------
    model_class : Type[T]
        The Django model class this repository handles
    """

    def __init__(self, model_class: Type[T]):  # noqa: C901
        self.model_class = model_class  # noqa: F811

    def get_queryset(self) -> QuerySet[T]:  # noqa: C901
        """
        Get the base queryset for the model.

        Returns
        -------
        QuerySet[T]
            Base queryset for the model
        """
        return self.model_class.objects.all()

    def create(self, **kwargs) -> T:  # noqa: C901
        """
        Create a new model instance.

        Parameters
        ----------
        **kwargs : dict
            Fields and values for the new instance

        Returns
        -------
        T
            The created model instance

        Raises
        ------
        ValidationError
            If the model data is invalid
        """
        instance = self.model_class(**kwargs)
        instance.full_clean()
        instance.save()
        return instance

    def update(self, instance: T, **kwargs) -> T:  # noqa: C901
        """
        Update an existing model instance.

        Parameters
        ----------
        instance : T
            The model instance to update
        **kwargs : dict
            Fields and values to update

        Returns
        -------
        T
            The updated model instance

        Raises
        ------
        ValidationError
            If the update data is invalid
        """
        for key, value in kwargs.items():
            setattr(instance, key, value)
        instance.full_clean()
        instance.save()
        return instance

    def delete(self, instance: T) -> bool:  # noqa: C901
        """
        Delete a model instance.

        Parameters
        ----------
        instance : T
            The model instance to delete

        Returns
        -------
        bool
            True if deletion was successful
        """
        instance.delete()
        return True

    def get_by_id(self, id) -> Optional[T]:  # noqa: C901
        """
        Get a model instance by its ID.

        Parameters
        ----------
        id : Any
            The primary key value

        Returns
        -------
        Optional[T]
            The model instance if found, None otherwise
        """
        try:
            return self.get_queryset().get(pk=id)
        except self.model_class.DoesNotExist:
            return None

    def get_by_ids(self, ids: List) -> QuerySet[T]:  # noqa: C901
        """
        Get multiple model instances by their IDs.

        Parameters
        ----------
        ids : List
            List of primary key values

        Returns
        -------
        QuerySet[T]
            Queryset of matching model instances
        """
        return self.get_queryset().filter(pk__in=ids)

    def exists(self, **kwargs) -> bool:  # noqa: C901
        """
        Check if any model instance matches the given criteria.

        Parameters
        ----------
        **kwargs : dict
            Lookup parameters

        Returns
        -------
        bool
            True if a matching instance exists
        """
        return self.get_queryset().filter(**kwargs).exists()

    def filter(self, **kwargs) -> QuerySet[T]:  # noqa: C901
        """
        Filter model instances by given criteria.

        Parameters
        ----------
        **kwargs : dict
            Filter parameters

        Returns
        -------
        QuerySet[T]
            Queryset of matching model instances
        """
        return self.get_queryset().filter(**kwargs)

    def all(self) -> QuerySet[T]:  # noqa: C901
        """
        Get all model instances.

        Returns
        -------
        QuerySet[T]
            Queryset of all model instances
        """
        return self.get_queryset()

    def count(self) -> int:  # noqa: C901
        """
        Count all model instances.

        Returns
        -------
        int
            Count of all model instances
        """
        return self.get_queryset().count()

    def first(self) -> Optional[T]:  # noqa: C901
        """
        Get the first model instance.

        Returns
        -------
        Optional[T]
            First model instance if any exists, None otherwise
        """
        return self.get_queryset().first()

    def last(self) -> Optional[T]:  # noqa: C901
        """
        Get the last model instance.

        Returns
        -------
        Optional[T]
            Last model instance if any exists, None otherwise
        """
        return self.get_queryset().last()

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This function is used by repositories to convert UUID strings to model instances
        before creating or updating model instances.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy of the data to avoid modifying the original
        data_copy = data.copy()
        # Handle entity_model field if it's a string (slug)
        if "entity_slug" in data_copy and isinstance(
            data_copy["entity_slug"], str
        ):
            try:
                # Convert entity_slug to entity_model
                entity_slug = data_copy.pop("entity_slug")
                EntityModel = apps.get_model("django_ledger", "EntityModel")
                data_copy["entity_model"] = get_object_or_404(
                    EntityModel, slug=entity_slug
                )
            except Exception:
                pass

        def process_value(value):  # noqa: C901
            if isinstance(value, dict):
                return self.convert_uuids_to_model_instances(value)
            elif isinstance(value, list):
                return [process_value(item) for item in value]
            elif isinstance(value, str):
                try:
                    # Try to parse as UUID to validate
                    uuid_obj = UUID(value)
                    return uuid_obj
                except ValueError:
                    return value
            return value

        # Process all fields in the data
        for field_name, field_value in list(data_copy.items()):
            # Skip None values and convert empty strings to None for ForeignKey fields
            if field_value is None:
                continue
            elif field_value == "":
                # Convert empty string to None for nullable ForeignKey fields
                data_copy[field_name] = None
                continue

            # Process the value recursively
            processed_value = process_value(field_value)
            # If the processed value is a UUID, try to find the corresponding model
            if isinstance(processed_value, UUID):
                # Pattern 1: field_name is a foreign key field ending with '_id'
                if field_name.endswith("_id"):
                    base_field = field_name[:-3]  # Remove '_id' suffix
                    model_name = (
                        "".join(
                            word.capitalize() for word in base_field.split("_")
                        )
                        + "Model"
                    )
                    try:
                        model_class = apps.get_model(
                            "django_ledger", model_name
                        )  # noqa: F811
                        instance = model_class.objects.get(uuid=processed_value)
                        data_copy[base_field] = instance
                        del data_copy[field_name]
                        continue
                    except (LookupError, ObjectDoesNotExist):
                        pass

                # Pattern 2: field_name directly corresponds to a model name
                model_name = (
                    "".join(word.capitalize() for word in field_name.split("_"))
                    + "Model"
                )
                try:
                    model_class = apps.get_model(
                        "django_ledger", model_name
                    )  # noqa: F811
                    instance = model_class.objects.get(uuid=processed_value)
                    data_copy[field_name] = instance
                    continue
                except (LookupError, ObjectDoesNotExist):
                    pass

                # Pattern 3: field_name is a foreign key field (without '_id' suffix)
                # This is a more general approach that tries to find any model with this UUID  # noqa: E501
                for app_config in apps.get_app_configs():
                    if app_config.name.startswith("django_ledger"):
                        for model_class in app_config.get_models():
                            if hasattr(model_class, "uuid"):
                                try:
                                    instance = model_class.objects.get(
                                        uuid=processed_value
                                    )
                                    data_copy[field_name] = instance
                                    break
                                except ObjectDoesNotExist:
                                    continue
            else:
                data_copy[field_name] = processed_value
        return data_copy
